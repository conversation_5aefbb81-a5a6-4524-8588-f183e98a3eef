// 现代卡片交互脚本
class ModernCardSwitcher {
    constructor() {
        this.currentStyle = 'glassmorphism';
        this.styleInfo = {
            glassmorphism: {
                title: '玻璃拟态风格',
                description: '采用半透明玻璃效果和模糊背景，配合霓虹色边框，营造现代科技感。这种设计深受年轻人喜爱，常见于iOS和现代Web应用中。',
                tags: ['现代感', '科技风', '年轻化']
            },
            neumorphism: {
                title: '新拟物化风格',
                description: '使用柔和阴影和高光效果，创造立体按压质感。这种设计风格在暗色主题中表现出色，给人高端、精致的感觉。',
                tags: ['立体感', '高端感', '精致感']
            },
            cyberpunk: {
                title: '霓虹赛博风格',
                description: '融合赛博朋克美学，使用霓虹色彩、网格背景和故障效果。这种风格深受游戏和科幻文化影响，充满未来感和叛逆精神。',
                tags: ['未来感', '叛逆风', '游戏感']
            },
            retro: {
                title: '复古简约风格',
                description: '结合80-90年代的设计元素，使用温暖的渐变色彩和像素风格字体。这种设计唤起怀旧情感，深受年轻人喜爱。',
                tags: ['怀旧感', '温暖感', '个性化']
            }
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateInfo();
        this.startAnimations();
    }

    bindEvents() {
        const buttons = document.querySelectorAll('.style-btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const style = button.getAttribute('data-style');
                this.switchStyle(style);
            });
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            const styles = ['glassmorphism', 'neumorphism', 'cyberpunk', 'retro'];
            const currentIndex = styles.indexOf(this.currentStyle);
            
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                e.preventDefault();
                const prevIndex = (currentIndex - 1 + styles.length) % styles.length;
                this.switchStyle(styles[prevIndex]);
            } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                e.preventDefault();
                const nextIndex = (currentIndex + 1) % styles.length;
                this.switchStyle(styles[nextIndex]);
            }
        });

        // 添加触摸滑动支持
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // 只处理水平滑动
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                const styles = ['glassmorphism', 'neumorphism', 'cyberpunk', 'retro'];
                const currentIndex = styles.indexOf(this.currentStyle);
                
                if (diffX > 0) {
                    // 向左滑动，显示下一个
                    const nextIndex = (currentIndex + 1) % styles.length;
                    this.switchStyle(styles[nextIndex]);
                } else {
                    // 向右滑动，显示上一个
                    const prevIndex = (currentIndex - 1 + styles.length) % styles.length;
                    this.switchStyle(styles[prevIndex]);
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }

    switchStyle(newStyle) {
        if (newStyle === this.currentStyle) return;

        // 更新按钮状态
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-style="${newStyle}"]`).classList.add('active');

        // 切换卡片
        document.querySelectorAll('.card').forEach(card => {
            card.classList.remove('active');
        });
        
        // 添加切换动画
        const newCard = document.getElementById(`${newStyle}-card`);
        setTimeout(() => {
            newCard.classList.add('active');
        }, 100);

        this.currentStyle = newStyle;
        this.updateInfo();
        this.triggerStyleSpecificAnimations(newStyle);
    }

    updateInfo() {
        const info = this.styleInfo[this.currentStyle];
        
        document.getElementById('current-style-title').textContent = info.title;
        document.getElementById('current-style-desc').textContent = info.description;
        
        // 更新标签
        const tags = document.querySelectorAll('.tag');
        tags.forEach((tag, index) => {
            if (info.tags[index]) {
                tag.textContent = info.tags[index];
                tag.style.display = 'inline-block';
            } else {
                tag.style.display = 'none';
            }
        });
    }

    triggerStyleSpecificAnimations(style) {
        switch(style) {
            case 'glassmorphism':
                this.animateGlassmorphism();
                break;
            case 'neumorphism':
                this.animateNeumorphism();
                break;
            case 'cyberpunk':
                this.animateCyberpunk();
                break;
            case 'retro':
                this.animateRetro();
                break;
        }
    }

    animateGlassmorphism() {
        const card = document.getElementById('glassmorphism-card');
        card.style.transform = 'scale(0.95)';
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 200);
    }

    animateNeumorphism() {
        const card = document.getElementById('neumorphism-card');
        card.style.boxShadow = `
            30px 30px 60px var(--neuro-shadow-dark),
            -30px -30px 60px var(--neuro-shadow-light)
        `;
        setTimeout(() => {
            card.style.boxShadow = `
                20px 20px 40px var(--neuro-shadow-dark),
                -20px -20px 40px var(--neuro-shadow-light)
            `;
        }, 300);
    }

    animateCyberpunk() {
        const scanLine = document.querySelector('.cyber-scan-line');
        if (scanLine) {
            scanLine.style.animation = 'none';
            setTimeout(() => {
                scanLine.style.animation = 'scan 2s linear infinite';
            }, 100);
        }
    }

    animateRetro() {
        const card = document.getElementById('retro-card');
        card.style.filter = 'hue-rotate(20deg)';
        setTimeout(() => {
            card.style.filter = 'hue-rotate(0deg)';
        }, 500);
    }

    startAnimations() {
        // 启动背景动画
        this.animateBackground();
        
        // 定期触发特效
        setInterval(() => {
            this.triggerRandomEffect();
        }, 5000);
    }

    animateBackground() {
        const body = document.body;
        let hue = 0;
        
        setInterval(() => {
            hue = (hue + 1) % 360;
            body.style.background = `linear-gradient(135deg, 
                hsl(${hue}, 70%, 60%) 0%, 
                hsl(${(hue + 60) % 360}, 70%, 60%) 100%)`;
        }, 100);
    }

    triggerRandomEffect() {
        const effects = ['pulse', 'glow', 'shake'];
        const randomEffect = effects[Math.floor(Math.random() * effects.length)];
        const activeCard = document.querySelector('.card.active');
        
        if (activeCard) {
            activeCard.classList.add(randomEffect);
            setTimeout(() => {
                activeCard.classList.remove(randomEffect);
            }, 1000);
        }
    }
}

// 添加特效CSS类
const style = document.createElement('style');
style.textContent = `
    .pulse {
        animation: pulse-effect 1s ease-in-out;
    }
    
    .glow {
        animation: glow-effect 1s ease-in-out;
    }
    
    .shake {
        animation: shake-effect 0.5s ease-in-out;
    }
    
    @keyframes pulse-effect {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
    
    @keyframes glow-effect {
        0%, 100% { filter: brightness(1); }
        50% { filter: brightness(1.2); }
    }
    
    @keyframes shake-effect {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ModernCardSwitcher();
    
    // 添加加载动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// 添加性能优化
if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
        // 预加载图片
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            const newImg = new Image();
            newImg.src = img.src;
        });
    });
}
