/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Serif SC', serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20px;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.main-title {
    text-align: center;
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 3rem;
    font-weight: 300;
    letter-spacing: 2px;
}

.section-title {
    font-size: 1.5rem;
    color: #34495e;
    margin-bottom: 1.5rem;
    font-weight: 400;
}

.card-section {
    margin-bottom: 4rem;
}

/* 方案一：诗意渐变卡片 */
.gradient-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 0;
    display: flex;
    align-items: center;
    min-height: 400px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.gradient-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.gradient-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.gradient-card .card-image {
    flex: 1;
    padding: 2rem;
}

.gradient-card .card-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.gradient-card .card-content {
    flex: 1.2;
    padding: 2rem;
    color: white;
    position: relative;
    z-index: 1;
}

.gradient-card .book-title {
    font-size: 1.8rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.gradient-card .author {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    font-style: italic;
}

.gradient-card .quote {
    font-size: 1.1rem;
    line-height: 1.8;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    font-weight: 300;
}

/* 方案二：文艺复古卡片 */
.vintage-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    min-height: 450px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    background: #f4f1e8;
}

.vintage-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.vintage-bg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: sepia(20%) saturate(80%);
}

.vintage-content {
    position: relative;
    z-index: 2;
    padding: 3rem;
    background: linear-gradient(45deg, rgba(244,241,232,0.95) 0%, rgba(244,241,232,0.9) 100%);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border: 3px solid #d4af37;
    border-radius: 15px;
}

.quote-mark {
    font-size: 4rem;
    color: #d4af37;
    font-family: 'Ma Shan Zheng', cursive;
    line-height: 1;
    margin-bottom: -1rem;
}

.quote-mark.closing {
    align-self: flex-end;
    margin-top: -1rem;
    margin-bottom: 0;
}

.vintage-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.vintage-card .book-info {
    margin-bottom: 1.5rem;
    text-align: center;
}

.vintage-card .book-title {
    font-size: 1.6rem;
    color: #8b4513;
    font-weight: 500;
    display: block;
    margin-bottom: 0.5rem;
}

.vintage-card .author {
    font-size: 1.1rem;
    color: #a0522d;
    font-style: italic;
}

.vintage-card .quote {
    font-size: 1.1rem;
    line-height: 1.9;
    color: #5d4037;
    text-align: justify;
    font-weight: 400;
    text-indent: 2em;
}

/* 方案三：现代简约卡片 */
.modern-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 500px;
    margin: 0 auto;
}

.modern-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.modern-image {
    height: 250px;
    overflow: hidden;
}

.modern-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.modern-card:hover .modern-image img {
    transform: scale(1.05);
}

.modern-content {
    padding: 2rem;
}

.modern-header {
    margin-bottom: 1.5rem;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.modern-card .book-title {
    font-size: 1.4rem;
    color: #2c3e50;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.modern-card .author {
    font-size: 1rem;
    color: #7f8c8d;
    font-style: italic;
}

.modern-quote {
    font-size: 1rem;
    line-height: 1.7;
    color: #34495e;
    text-align: justify;
    font-weight: 300;
}

/* 网格布局 */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.cards-grid .card.variant {
    max-width: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .gradient-card {
        flex-direction: column;
        min-height: auto;
    }
    
    .gradient-card .card-image,
    .gradient-card .card-content {
        flex: none;
        padding: 1.5rem;
    }
    
    .vintage-content {
        padding: 2rem;
    }
    
    .modern-content {
        padding: 1.5rem;
    }
    
    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
}
