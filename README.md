# 文学书摘卡片设计

这是一个美观的文学书摘卡片设计项目，包含三种不同风格的卡片模板，适合展示文学作品的精美摘录。

## 📁 文件说明

- `literary-cards.html` - 完整的展示页面，包含所有三种卡片样式
- `card-template.html` - 单独的卡片模板，方便自定义使用
- `styles.css` - 主要样式文件
- `README.md` - 使用说明文档

## 🎨 三种卡片样式（已优化）

### 1. 诗意渐变卡片
- **特色**：柔和的紫色渐变背景，营造梦幻诗意的氛围
- **布局**：图文叠加设计，采用QQ邮箱贺卡比例（700x400px）
- **适用场景**：浪漫文学、诗歌、散文摘录

### 2. 文艺复古卡片
- **特色**：复古纸质纹理，金色装饰边框，经典引号设计
- **布局**：图片作为背景，文字叠加，背景图片透明度提高至60%
- **适用场景**：经典文学、哲学作品、历史文献

### 3. 现代简约卡片
- **特色**：简洁的白色背景，图文叠加设计
- **布局**：作者和书名位于右下角，格式为"——《书名》作者"
- **适用场景**：现代文学、商业书籍、学术文章

## 🖼️ 图片素材

项目包含5张精美的动物主题插画：
- 樱花下的猫咪
- 金色小熊与蜻蜓  
- 紫丁香下的兔子
- 小猪与瓢虫
- 花园中的小猫

## 🛠️ 自定义使用方法

### 方法一：使用模板文件
1. 打开 `card-template.html`
2. 选择你喜欢的样式（取消注释对应的HTML代码）
3. 替换以下内容：
   - 图片路径：修改 `src` 属性
   - 书名：修改 `.book-title` 内容
   - 作者：修改 `.author` 内容  
   - 摘录文字：修改 `.quote` 内容

### 方法二：复制样式代码
1. 从 `styles.css` 中复制你需要的样式类
2. 在你的项目中引用对应的HTML结构
3. 根据需要调整颜色、字体、尺寸等

## 🎯 样式类名参考

### 诗意渐变卡片
```html
<div class="card gradient-card">
    <div class="card-image">
        <img src="your-image.jpg" alt="描述" />
    </div>
    <div class="card-content">
        <div class="book-title">书名</div>
        <div class="author">作者</div>
        <div class="quote">摘录内容</div>
    </div>
</div>
```

### 文艺复古卡片
```html
<div class="card vintage-card">
    <div class="vintage-bg">
        <img src="your-image.jpg" alt="背景" />
    </div>
    <div class="vintage-content">
        <div class="quote-mark">"</div>
        <div class="vintage-text">
            <div class="book-info">
                <span class="book-title">书名</span>
                <span class="author">— 作者</span>
            </div>
            <div class="quote">摘录内容</div>
        </div>
        <div class="quote-mark closing">"</div>
    </div>
</div>
```

### 现代简约卡片
```html
<div class="card modern-card">
    <div class="modern-image">
        <img src="your-image.jpg" alt="插图" />
    </div>
    <div class="modern-content">
        <div class="modern-header">
            <h3 class="book-title">书名</h3>
            <p class="author">作者</p>
        </div>
        <div class="modern-quote">摘录内容</div>
    </div>
</div>
```

## 📱 响应式设计

所有卡片都支持响应式设计，在移动设备上会自动调整布局：
- 渐变卡片：从左右布局变为上下布局
- 复古卡片：减少内边距，保持整体效果
- 简约卡片：保持上下布局，调整尺寸

## 🎨 自定义建议

### 颜色调整
- 渐变卡片：修改 `linear-gradient` 中的颜色值
- 复古卡片：调整 `#d4af37`（金色）和背景色
- 简约卡片：修改 `#2c3e50`（深蓝）和 `#7f8c8d`（灰色）

### 字体调整
- 主要字体：`'Noto Serif SC'` （思源宋体）
- 装饰字体：`'Ma Shan Zheng'` （马善政楷体）
- 可以替换为其他中文字体

### 尺寸调整
- 卡片宽度：修改 `.card` 的 `max-width`
- 图片高度：调整 `.modern-image` 的 `height`
- 字体大小：修改各个 `font-size` 属性

## 🌟 使用技巧

1. **图片选择**：建议使用高质量、色彩柔和的插画或摄影作品
2. **文字长度**：摘录文字建议控制在100-200字之间，保持视觉平衡
3. **色彩搭配**：可以根据图片的主色调调整卡片的配色方案
4. **字体层次**：保持书名、作者、正文的字体大小层次分明

## 📄 许可证

本项目仅供学习和个人使用。如需商业使用，请确保图片素材的版权合规。
