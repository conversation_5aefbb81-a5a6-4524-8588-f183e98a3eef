<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>年轻人喜爱的文学卡片设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;700&family=Orbitron:wght@400;700;900&family=Space+Mono:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="modern-styles.css">
</head>
<body>
    <div class="container">
        <header class="main-header">
            <h1 class="main-title">年轻人喜爱的文学卡片</h1>
            <p class="subtitle">基于Gen Z设计趋势的现代化竖版卡片</p>
        </header>

        <div class="controls">
            <button class="style-btn active" data-style="glassmorphism">
                <span class="btn-icon">🔮</span>
                <span class="btn-text">玻璃拟态</span>
            </button>
            <button class="style-btn" data-style="neumorphism">
                <span class="btn-icon">🌙</span>
                <span class="btn-text">新拟物化</span>
            </button>
            <button class="style-btn" data-style="cyberpunk">
                <span class="btn-icon">⚡</span>
                <span class="btn-text">霓虹赛博</span>
            </button>
            <button class="style-btn" data-style="retro">
                <span class="btn-icon">📼</span>
                <span class="btn-text">复古简约</span>
            </button>
        </div>

        <div class="card-showcase">
            <!-- Glassmorphism 玻璃拟态卡片 -->
            <div class="card glassmorphism-card active" id="glassmorphism-card">
                <div class="card-background">
                    <div class="bg-blur"></div>
                    <img src="图片/20250505_1240_Кот в гамаке сакуры_simple_compose_01jtfbvb2meh5vmc0mzsw543ce.png" alt="樱花下的猫咪" class="bg-image" />
                </div>
                <div class="glass-overlay">
                    <div class="card-content">
                        <div class="image-container">
                            <img src="图片/20250505_1240_Кот в гамаке сакуры_simple_compose_01jtfbvb2meh5vmc0mzsw543ce.png" alt="樱花下的猫咪" class="main-image" />
                            <div class="image-glow"></div>
                        </div>
                        <div class="text-content">
                            <div class="quote-text">
                                全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                            </div>
                            <div class="attribution">
                                <div class="book-title">科尔索林最后的夏天</div>
                                <div class="author">黑塞</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="neon-border"></div>
            </div>

            <!-- Neumorphism 新拟物化卡片 -->
            <div class="card neumorphism-card" id="neumorphism-card">
                <div class="neuro-container">
                    <div class="image-section">
                        <div class="neuro-image-frame">
                            <img src="图片/20250505_1250_Золотистый медвежонок и стрекоза_simple_compose_01jtfcex4xf5frd6e9nby2emms.png" alt="金色小熊与蜻蜓" class="neuro-image" />
                        </div>
                    </div>
                    <div class="neuro-content">
                        <div class="neuro-quote">
                            全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                        </div>
                        <div class="neuro-attribution">
                            <div class="neuro-book">科尔索林最后的夏天</div>
                            <div class="neuro-author">黑塞</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cyberpunk 霓虹赛博卡片 -->
            <div class="card cyberpunk-card" id="cyberpunk-card">
                <div class="cyber-background">
                    <div class="cyber-grid"></div>
                    <div class="cyber-particles"></div>
                </div>
                <div class="cyber-content">
                    <div class="cyber-image-container">
                        <div class="cyber-frame">
                            <img src="图片/20250505_1254_Кролик под Сиренью_simple_compose_01jtfcnzf5ewwvdfnxrd7cxyve.png" alt="紫丁香下的兔子" class="cyber-image" />
                            <div class="cyber-scan-line"></div>
                        </div>
                    </div>
                    <div class="cyber-text">
                        <div class="cyber-quote">
                            <span class="glitch-text">全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。</span>
                        </div>
                        <div class="cyber-attribution">
                            <div class="cyber-book">科尔索林最后的夏天</div>
                            <div class="cyber-author">// 黑塞</div>
                        </div>
                    </div>
                </div>
                <div class="cyber-border"></div>
            </div>

            <!-- Retro 复古简约卡片 -->
            <div class="card retro-card" id="retro-card">
                <div class="retro-container">
                    <div class="retro-header">
                        <div class="retro-logo">📖</div>
                        <div class="retro-title">LITERARY QUOTE</div>
                    </div>
                    <div class="retro-image-section">
                        <div class="retro-frame">
                            <img src="图片/20250505_1258_Поросёнок и Божья Коровка_simple_compose_01jtfcxhmxff68kzrfkgzymeky.png" alt="小猪与瓢虫" class="retro-image" />
                            <div class="retro-filter"></div>
                        </div>
                    </div>
                    <div class="retro-content">
                        <div class="retro-quote">
                            全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                        </div>
                        <div class="retro-attribution">
                            <div class="retro-book">科尔索林最后的夏天</div>
                            <div class="retro-author">黑塞</div>
                        </div>
                    </div>
                    <div class="retro-footer">
                        <div class="retro-pattern"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <div class="info-content">
                <h3 class="info-title" id="current-style-title">玻璃拟态风格</h3>
                <p class="info-description" id="current-style-desc">
                    采用半透明玻璃效果和模糊背景，配合霓虹色边框，营造现代科技感。这种设计深受年轻人喜爱，常见于iOS和现代Web应用中。
                </p>
                <div class="info-tags">
                    <span class="tag" id="tag1">现代感</span>
                    <span class="tag" id="tag2">科技风</span>
                    <span class="tag" id="tag3">年轻化</span>
                </div>
            </div>
        </div>
    </div>

    <script src="modern-script.js"></script>
</body>
</html>
