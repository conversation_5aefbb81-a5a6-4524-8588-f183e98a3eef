/* 基础样式和变量 */
:root {
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --neon-primary: #00ffff;
    --neon-secondary: #ff00ff;
    --neuro-bg: #e0e5ec;
    --neuro-shadow-light: #ffffff;
    --neuro-shadow-dark: #a3b1c6;
    --cyber-primary: #00ff41;
    --cyber-secondary: #ff0080;
    --retro-primary: #ff6b35;
    --retro-secondary: #f7931e;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Serif SC', serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    overflow-x: hidden;
}

.container {
    max-width: 500px;
    margin: 0 auto;
}

/* 头部样式 */
.main-header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.main-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 300;
}

/* 控制按钮 */
.controls {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.style-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 0.8rem;
    min-width: 80px;
}

.style-btn:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
}

.style-btn.active {
    background: linear-gradient(45deg, var(--neon-primary), var(--neon-secondary));
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.btn-icon {
    font-size: 1.2rem;
}

.btn-text {
    font-size: 0.7rem;
    font-weight: 500;
}

/* 卡片展示区域 */
.card-showcase {
    position: relative;
    height: 600px;
    margin-bottom: 2rem;
}

.card {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;
    height: 600px;
    border-radius: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
}

.card.active {
    opacity: 1;
    visibility: visible;
}

/* Glassmorphism 玻璃拟态样式 */
.glassmorphism-card {
    position: relative;
    overflow: hidden;
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.bg-blur {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(20px);
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
    z-index: 2;
}

.bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.glass-overlay {
    position: relative;
    z-index: 3;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
}

.image-container {
    position: relative;
    width: 100%;
    height: 200px;
    margin-bottom: 2rem;
    border-radius: 15px;
    overflow: hidden;
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.image-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.3), transparent);
    border-radius: 15px;
    animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.text-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: white;
}

.quote-text {
    font-size: 1rem;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.attribution {
    text-align: right;
}

.book-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.3rem;
    color: var(--neon-primary);
}

.author {
    font-size: 0.9rem;
    opacity: 0.8;
    font-style: italic;
}

.neon-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-radius: 20px;
    background: linear-gradient(45deg, var(--neon-primary), var(--neon-secondary), var(--neon-primary));
    background-size: 200% 200%;
    animation: neon-flow 3s ease-in-out infinite;
    z-index: 4;
    pointer-events: none;
}

@keyframes neon-flow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Neumorphism 新拟物化样式 */
.neumorphism-card {
    background: var(--neuro-bg);
    box-shadow: 
        20px 20px 40px var(--neuro-shadow-dark),
        -20px -20px 40px var(--neuro-shadow-light);
    border-radius: 30px;
    padding: 2rem;
}

.neuro-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.image-section {
    height: 200px;
    margin-bottom: 2rem;
}

.neuro-image-frame {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    background: var(--neuro-bg);
    box-shadow: 
        inset 10px 10px 20px var(--neuro-shadow-dark),
        inset -10px -10px 20px var(--neuro-shadow-light);
    padding: 1rem;
    overflow: hidden;
}

.neuro-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.neuro-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.neuro-quote {
    font-size: 1rem;
    line-height: 1.6;
    color: #5a6c7d;
    text-align: justify;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    border-radius: 15px;
    background: var(--neuro-bg);
    box-shadow: 
        inset 5px 5px 10px var(--neuro-shadow-dark),
        inset -5px -5px 10px var(--neuro-shadow-light);
}

.neuro-attribution {
    text-align: right;
    padding: 1rem;
    border-radius: 15px;
    background: var(--neuro-bg);
    box-shadow: 
        5px 5px 10px var(--neuro-shadow-dark),
        -5px -5px 10px var(--neuro-shadow-light);
}

.neuro-book {
    font-size: 1rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.3rem;
}

.neuro-author {
    font-size: 0.9rem;
    color: #718096;
    font-style: italic;
}

/* 信息面板 */
.info-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
}

.info-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 0.8rem;
    color: var(--neon-primary);
}

.info-description {
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.info-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    padding: 0.3rem 0.8rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Cyberpunk 霓虹赛博样式 */
.cyberpunk-card {
    background: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
    border: 1px solid var(--cyber-primary);
    box-shadow:
        0 0 20px rgba(0, 255, 65, 0.3),
        inset 0 0 20px rgba(0, 255, 65, 0.1);
    position: relative;
    overflow: hidden;
}

.cyber-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.cyber-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 65, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 65, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: grid-move 10s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

.cyber-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 30%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(0, 255, 65, 0.1) 0%, transparent 50%);
    animation: particle-float 8s ease-in-out infinite;
}

@keyframes particle-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.cyber-content {
    position: relative;
    z-index: 2;
    height: 100%;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    font-family: 'Orbitron', monospace;
}

.cyber-image-container {
    height: 200px;
    margin-bottom: 2rem;
}

.cyber-frame {
    position: relative;
    width: 100%;
    height: 100%;
    border: 2px solid var(--cyber-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.5);
}

.cyber-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: contrast(1.2) saturate(1.3);
}

.cyber-scan-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-primary), transparent);
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% { transform: translateY(0); }
    100% { transform: translateY(200px); }
}

.cyber-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: var(--cyber-primary);
}

.cyber-quote {
    font-size: 0.95rem;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 1.5rem;
    text-shadow: 0 0 5px rgba(0, 255, 65, 0.5);
}

.glitch-text {
    animation: glitch 3s infinite;
}

@keyframes glitch {
    0%, 90%, 100% { transform: translateX(0); }
    20% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    60% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
}

.cyber-attribution {
    text-align: right;
    border: 1px solid var(--cyber-secondary);
    padding: 1rem;
    border-radius: 5px;
    background: rgba(255, 0, 128, 0.1);
}

.cyber-book {
    font-size: 1rem;
    font-weight: 700;
    color: var(--cyber-secondary);
    margin-bottom: 0.3rem;
    text-transform: uppercase;
}

.cyber-author {
    font-size: 0.9rem;
    color: var(--cyber-primary);
    font-family: 'Space Mono', monospace;
}

.cyber-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-radius: 20px;
    background: linear-gradient(45deg, var(--cyber-primary), var(--cyber-secondary), var(--cyber-primary));
    background-size: 200% 200%;
    animation: cyber-pulse 4s ease-in-out infinite;
    z-index: 3;
    pointer-events: none;
}

@keyframes cyber-pulse {
    0%, 100% {
        background-position: 0% 50%;
        box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    }
    50% {
        background-position: 100% 50%;
        box-shadow: 0 0 30px rgba(255, 0, 128, 0.5);
    }
}

/* Retro 复古简约样式 */
.retro-card {
    background: linear-gradient(135deg, #ff6b35, #f7931e, #ffcc02);
    border-radius: 15px;
    overflow: hidden;
    box-shadow:
        0 10px 30px rgba(255, 107, 53, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
}

.retro-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.retro-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.retro-logo {
    font-size: 1.5rem;
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.retro-title {
    font-family: 'Space Mono', monospace;
    font-size: 0.8rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
}

.retro-image-section {
    height: 180px;
    padding: 1rem;
}

.retro-frame {
    position: relative;
    width: 100%;
    height: 100%;
    border: 3px solid white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        0 5px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.retro-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: sepia(20%) saturate(1.2) contrast(1.1);
}

.retro-filter {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%),
        radial-gradient(circle at 30% 30%, rgba(255, 204, 2, 0.2) 0%, transparent 50%);
    animation: retro-shine 4s ease-in-out infinite;
}

@keyframes retro-shine {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

.retro-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: white;
}

.retro-quote {
    font-size: 0.95rem;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid white;
}

.retro-attribution {
    text-align: right;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.retro-book {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    font-family: 'Space Mono', monospace;
}

.retro-author {
    font-size: 0.9rem;
    opacity: 0.9;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.retro-footer {
    height: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.retro-pattern {
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 10px,
            rgba(255, 255, 255, 0.1) 10px,
            rgba(255, 255, 255, 0.1) 20px
        );
    animation: pattern-move 3s linear infinite;
}

@keyframes pattern-move {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .card {
        width: 100%;
        max-width: 350px;
        height: 500px;
    }

    .card-showcase {
        height: 500px;
    }

    .glass-overlay,
    .cyber-content,
    .retro-content {
        padding: 1.5rem;
    }

    .image-container,
    .cyber-image-container,
    .retro-image-section {
        height: 120px;
    }

    .quote-text,
    .cyber-quote,
    .retro-quote {
        font-size: 0.85rem;
    }

    .neuro-content {
        padding: 1rem;
    }
}
