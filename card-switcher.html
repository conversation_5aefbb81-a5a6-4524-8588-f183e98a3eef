<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文学卡片样式切换器</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@300;400;500;700&family=Ma+<PERSON>+<PERSON>&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .style-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            background: white;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            font-size: 0.9rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .style-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .style-btn.active {
            background: #667eea;
            color: white;
        }

        .card-container {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .card {
            width: 700px;
            height: 400px;
            transition: all 0.5s ease;
        }

        /* 样式1：诗意渐变（改为叠加设计） */
        .gradient-style {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .gradient-style .image-section {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.4;
        }

        .gradient-style .image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .gradient-style .content-section {
            position: relative;
            z-index: 2;
            padding: 3rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
        }

        .gradient-style .book-title {
            font-size: 1.6rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .gradient-style .author {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            font-style: italic;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .gradient-style .quote {
            font-size: 1.1rem;
            line-height: 1.8;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
            font-weight: 300;
            text-align: justify;
        }

        /* 样式2：文艺复古（降低透明度） */
        .vintage-style {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            height: 400px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            background: #f4f1e8;
        }

        .vintage-style .bg-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .vintage-style .bg-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: sepia(20%) saturate(80%);
        }

        .vintage-style .content-overlay {
            position: relative;
            z-index: 2;
            padding: 3rem;
            background: linear-gradient(45deg, rgba(244,241,232,0.7) 0%, rgba(244,241,232,0.6) 100%);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            border: 3px solid #d4af37;
            border-radius: 15px;
        }

        .vintage-style .quote-mark {
            font-size: 3rem;
            color: #d4af37;
            font-family: 'Ma Shan Zheng', cursive;
            line-height: 1;
        }

        .vintage-style .book-info {
            margin: 1.5rem 0;
            text-align: center;
        }

        .vintage-style .book-title {
            font-size: 1.4rem;
            color: #8b4513;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .vintage-style .author {
            font-size: 1rem;
            color: #a0522d;
            font-style: italic;
        }

        .vintage-style .quote {
            font-size: 1rem;
            line-height: 1.8;
            color: #5d4037;
            text-align: justify;
            text-indent: 2em;
        }

        /* 样式3：现代简约（上图下文，中间过渡线） */
        .modern-style {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            height: 400px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
        }

        .modern-style .image-section {
            height: 200px;
            overflow: hidden;
            position: relative;
        }

        .modern-style .image-section img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .modern-style:hover .image-section img {
            transform: scale(1.05);
        }

        .modern-style .image-section::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .modern-style .content-section {
            flex: 1;
            padding: 2rem;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .modern-style .quote {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2c3e50;
            text-align: justify;
            margin-bottom: auto;
            font-weight: 400;
        }

        .modern-style .attribution {
            text-align: right;
            font-size: 1rem;
            color: #7f8c8d;
            font-style: italic;
            margin-top: 1.5rem;
        }

        .hidden {
            display: none;
        }

        .info-panel {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .info-title {
            font-size: 1.2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .info-text {
            color: #7f8c8d;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .card {
                width: 100%;
                max-width: 500px;
                height: 300px;
            }

            .gradient-style .content-section,
            .vintage-style .content-overlay,
            .modern-style .content-section {
                padding: 2rem;
            }

            .gradient-style .quote,
            .vintage-style .quote,
            .modern-style .quote {
                font-size: 0.95rem;
                line-height: 1.6;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">文学卡片样式切换器</h1>
            <p style="color: #7f8c8d;">点击下方按钮切换不同的卡片样式</p>
        </div>

        <div class="controls">
            <button class="style-btn active" onclick="switchStyle('gradient')">诗意渐变</button>
            <button class="style-btn" onclick="switchStyle('vintage')">文艺复古</button>
            <button class="style-btn" onclick="switchStyle('modern')">现代简约</button>
        </div>

        <div class="card-container">
            <!-- 诗意渐变卡片 -->
            <div id="gradient-card" class="card gradient-style">
                <div class="image-section">
                    <img src="图片/20250505_1240_Кот в гамаке сакуры_simple_compose_01jtfbvb2meh5vmc0mzsw543ce.png" alt="樱花下的猫咪" />
                </div>
                <div class="content-section">
                    <div class="book-title">《科尔索林最后的夏天》</div>
                    <div class="author">黑塞</div>
                    <div class="quote">
                        全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                    </div>
                </div>
            </div>

            <!-- 文艺复古卡片 -->
            <div id="vintage-card" class="card vintage-style hidden">
                <div class="bg-image">
                    <img src="图片/20250505_1250_Золотистый медвежонок и стрекоза_simple_compose_01jtfcex4xf5frd6e9nby2emms.png" alt="金色小熊与蜻蜓" />
                </div>
                <div class="content-overlay">
                    <div class="quote-mark">"</div>
                    <div class="book-info">
                        <div class="book-title">科尔索林最后的夏天</div>
                        <div class="author">— 黑塞</div>
                    </div>
                    <div class="quote">
                        全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                    </div>
                    <div class="quote-mark" style="text-align: right;">"</div>
                </div>
            </div>

            <!-- 现代简约卡片 -->
            <div id="modern-card" class="card modern-style hidden">
                <div class="image-section">
                    <img src="图片/20250505_1254_Кролик под Сиренью_simple_compose_01jtfcnzf5ewwvdfnxrd7cxyve.png" alt="紫丁香下的兔子" />
                </div>
                <div class="content-section">
                    <div class="quote">
                        全世界的水都会重逢，北冰洋与尼罗河会在湿云中交融，这古老美丽的比喻让此刻变得神圣，即使漫游，每条路也都会带我们归家。
                    </div>
                    <div class="attribution">
                        ——《科尔索林最后的夏天》黑塞
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <div class="info-title" id="style-info-title">诗意渐变卡片</div>
            <div class="info-text" id="style-info-text">
                采用柔和的紫色渐变背景，营造梦幻诗意的氛围。左右布局设计，图片与文字相得益彰，适合展示浪漫文学、诗歌和散文摘录。
            </div>
        </div>
    </div>

    <script>
        const styleInfo = {
            gradient: {
                title: '诗意渐变卡片',
                text: '采用QQ邮箱贺卡比例，图文叠加设计，柔和的紫色渐变背景营造梦幻诗意氛围。文字叠加在半透明图片上，适合展示浪漫文学、诗歌和散文摘录。'
            },
            vintage: {
                title: '文艺复古卡片',
                text: '复古纸质纹理配合金色装饰边框，经典引号设计增添文艺气息。背景图片透明度提高，让图片更清晰可见，适合展示经典文学、哲学作品和历史文献。'
            },
            modern: {
                title: '现代简约卡片',
                text: '简洁的白色背景，图文叠加设计，作者和书名位于右下角。采用"——书名 作者"的经典格式，适合展示现代文学、商业书籍和学术文章。'
            }
        };

        function switchStyle(style) {
            // 隐藏所有卡片
            document.querySelectorAll('.card').forEach(card => {
                card.classList.add('hidden');
            });

            // 显示选中的卡片
            document.getElementById(style + '-card').classList.remove('hidden');

            // 更新按钮状态
            document.querySelectorAll('.style-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新信息面板
            document.getElementById('style-info-title').textContent = styleInfo[style].title;
            document.getElementById('style-info-text').textContent = styleInfo[style].text;
        }
    </script>
</body>
</html>
